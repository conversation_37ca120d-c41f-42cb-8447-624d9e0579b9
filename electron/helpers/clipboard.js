import { clipboard, nativeImage } from 'electron'
import fs from 'node:fs'
import path from 'node:path'
import { Buff<PERSON> } from 'node:buffer'

/**
 * 将文件复制到系统剪切板
 * 支持 macOS、Windows、Linux 平台，能够复制任意类型的文件
 *
 * @param {string} filePath - 要复制的文件路径
 * @returns {Promise<boolean>} - 操作是否成功
 */
export async function copyFileToClipboard(filePath) {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      throw new Error(`File does not exist: ${filePath}`)
    }

    // 获取文件信息
    const stats = fs.statSync(filePath)
    if (!stats.isFile()) {
      throw new Error('Path is not a file')
    }

    const ext = path.extname(filePath).toLowerCase()
    const absolutePath = path.resolve(filePath)
    const platform = process.platform

    // 检查是否为图片文件
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.ico', '.tiff', '.tif']
    const isImageFile = imageExtensions.includes(ext)

    // 对于图片文件，同时复制图片内容和文件路径
    if (isImageFile) {
      await copyImageFile(absolutePath, platform)
    }
    else {
      // 对于非图片文件，只复制文件路径
      await copyFilePath(absolutePath, platform)
    }

    console.log(`Successfully copied file to clipboard: ${filePath}`)
    return true
  }
  catch (error) {
    console.error('Failed to copy file to clipboard:', error.message)
    return false
  }
}

/**
 * 复制图片文件到剪切板（同时包含图片内容和文件路径）
 * @param {string} absolutePath - 文件的绝对路径
 * @param {string} platform - 操作系统平台
 */
async function copyImageFile(absolutePath, platform) {
  try {
    // 读取文件内容并创建图片对象
    const imageBuffer = fs.readFileSync(absolutePath)
    const image = nativeImage.createFromBuffer(imageBuffer)

    if (!image.isEmpty()) {
      // 复制图片内容
      clipboard.writeImage(image)
      console.log('Image content copied to clipboard')
    }

    // 同时复制文件路径，以便支持文件操作
    await copyFilePath(absolutePath, platform)
  }
  catch (error) {
    console.warn('Failed to copy image content, falling back to file path only:', error.message)
    // 如果图片复制失败，至少复制文件路径
    await copyFilePath(absolutePath, platform)
  }
}

/**
 * 复制文件路径到剪切板
 * @param {string} absolutePath - 文件的绝对路径
 * @param {string} platform - 操作系统平台
 */
async function copyFilePath(absolutePath, platform) {
  switch (platform) {
    case 'darwin': // macOS
      await copyFilePathMacOS(absolutePath)
      break

    case 'win32': // Windows
      await copyFilePathWindows(absolutePath)
      break

    case 'linux': // Linux
      await copyFilePathLinux(absolutePath)
      break

    default:
      console.warn(`Platform ${platform} may not be fully supported for file clipboard operations`)
      // 尝试使用 Linux 格式作为后备
      await copyFilePathLinux(absolutePath)
      break
  }
}

/**
 * macOS 平台文件路径复制
 * 使用 NSFilenamesPboardType 格式（plist 格式）
 */
async function copyFilePathMacOS(absolutePath) {
  try {
    // 创建 plist 格式的文件路径数据
    const plistContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<array>
  <string>${absolutePath}</string>
</array>
</plist>`

    const buffer = Buffer.from(plistContent, 'utf8')
    clipboard.writeBuffer('NSFilenamesPboardType', buffer)
    console.log('File path copied to clipboard (macOS format)')
  }
  catch (error) {
    console.error('Failed to copy file path on macOS:', error.message)
    throw error
  }
}

/**
 * Windows 平台文件路径复制
 * 使用 FileNameW 格式（UTF-16 编码）
 */
async function copyFilePathWindows(absolutePath) {
  try {
    // Windows 文件路径需要使用反斜杠
    const windowsPath = absolutePath.replace(/\//g, '\\')

    // 创建以 null 结尾的 UTF-16 字符串
    const pathWithNull = `${windowsPath}\0`
    const buffer = Buffer.from(pathWithNull, 'utf16le')

    clipboard.writeBuffer('FileNameW', buffer)
    console.log('File path copied to clipboard (Windows format)')
  }
  catch (error) {
    console.error('Failed to copy file path on Windows:', error.message)
    throw error
  }
}

/**
 * Linux 平台文件路径复制
 * 使用 text/uri-list 格式
 */
async function copyFilePathLinux(absolutePath) {
  try {
    // Linux 使用 file:// URI 格式
    const fileUri = `file://${absolutePath}\n`
    const buffer = Buffer.from(fileUri, 'utf8')

    clipboard.writeBuffer('text/uri-list', buffer)
    console.log('File path copied to clipboard (Linux format)')
  }
  catch (error) {
    console.error('Failed to copy file path on Linux:', error.message)
    throw error
  }
}
